#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EsafeNet 最终删除工具
专门处理系统驱动文件和顽固文件的删除
"""

import subprocess
import time
import logging
import sys
import os
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_admin_privileges():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False

def create_reboot_delete_script():
    """创建重启后删除的脚本"""
    
    # 重启后删除脚本内容
    script_content = '''@echo off
chcp 65001 >nul
echo ========================================
echo EsafeNet 重启后删除工具
echo ========================================
echo.

set "TARGET_DIR=C:\\Program Files\\EsafeNet\\Cobra DocGuard Client"

echo 等待系统完全启动...
timeout /t 15 /nobreak >nul

echo 正在删除EsafeNet文件...
echo 目标目录: %TARGET_DIR%
echo.

if not exist "%TARGET_DIR%" (
    echo 目录不存在，可能已被删除
    goto :cleanup
)

:: 强制删除所有文件
for %%f in ("%TARGET_DIR%\\*.*") do (
    echo 正在处理: %%~nxf
    
    :: 获取所有权
    takeown /f "%%f" /a >nul 2>&1
    
    :: 设置权限
    icacls "%%f" /grant administrators:F >nul 2>&1
    
    :: 移除属性
    attrib -r -s -h "%%f" >nul 2>&1
    
    :: 删除文件
    del /f /q "%%f" >nul 2>&1
    
    if exist "%%f" (
        echo   失败: %%~nxf
    ) else (
        echo   成功: %%~nxf
    )
)

:: 尝试删除目录
rmdir "%TARGET_DIR%" >nul 2>&1
if not exist "%TARGET_DIR%" (
    echo.
    echo 目录删除成功！
) else (
    echo.
    echo 目录仍然存在，可能包含子目录
)

:cleanup
echo.
echo 删除操作完成
echo 脚本将在5秒后自动关闭并删除自身
timeout /t 5 >nul

:: 删除自身
del "%~f0" >nul 2>&1
'''

    # 获取启动文件夹路径
    startup_folder = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
    startup_folder.mkdir(parents=True, exist_ok=True)
    
    # 创建批处理文件
    bat_file = startup_folder / "esafenet_reboot_delete.bat"
    
    try:
        with open(bat_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        logger.info(f"✅ 重启删除脚本已创建: {bat_file}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建重启脚本失败: {e}")
        return False

def immediate_delete_attempt():
    """立即尝试删除"""
    target_directory = r"C:\Program Files\EsafeNet\Cobra DocGuard Client"
    target_dir = Path(target_directory)
    
    if not target_dir.exists():
        logger.info("✅ 目标目录不存在，可能已被删除")
        return True
    
    logger.info("🔄 尝试立即删除...")
    
    # 停止资源管理器
    logger.info("正在停止资源管理器...")
    try:
        subprocess.run(['taskkill', '/f', '/im', 'explorer.exe'], capture_output=True)
        time.sleep(3)
    except Exception as e:
        logger.warning(f"停止资源管理器失败: {e}")
    
    # 停止相关进程和服务
    logger.info("正在停止相关进程和服务...")
    
    # 停止服务
    services = ['CommonService', 'HnCommonService']
    for service in services:
        try:
            subprocess.run(['net', 'stop', service, '/y'], capture_output=True, timeout=30)
            subprocess.run(['sc', 'stop', service], capture_output=True, timeout=30)
        except Exception:
            pass
    
    # 停止进程
    processes = ['CommonService.exe', 'HnCommonService.exe']
    for process in processes:
        try:
            subprocess.run(['taskkill', '/f', '/im', process], capture_output=True)
        except Exception:
            pass
    
    time.sleep(5)
    
    # 尝试删除文件
    files = list(target_dir.iterdir())
    deleted_count = 0
    failed_files = []
    
    for file_path in files:
        logger.info(f"正在处理: {file_path.name}")
        
        try:
            # 方法1: 获取所有权并删除
            subprocess.run(['takeown', '/f', str(file_path), '/a'], capture_output=True, timeout=30)
            subprocess.run(['icacls', str(file_path), '/grant', 'administrators:F'], capture_output=True, timeout=30)
            subprocess.run(['attrib', '-r', '-s', '-h', str(file_path)], capture_output=True)
            
            file_path.unlink()
            logger.info(f"  ✅ 删除成功: {file_path.name}")
            deleted_count += 1
            
        except Exception as e:
            logger.warning(f"  ❌ 删除失败: {file_path.name} - {e}")
            failed_files.append(str(file_path))
    
    # 重启资源管理器
    logger.info("正在重启资源管理器...")
    try:
        subprocess.Popen(['explorer.exe'])
        time.sleep(2)
    except Exception as e:
        logger.warning(f"重启资源管理器失败: {e}")
    
    # 检查结果
    remaining_files = list(target_dir.iterdir()) if target_dir.exists() else []
    
    if not remaining_files:
        logger.info("✅ 所有文件删除成功！")
        return True
    else:
        logger.warning(f"❌ 仍有 {len(remaining_files)} 个文件未删除")
        for file_path in remaining_files:
            logger.warning(f"  - {file_path.name}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("EsafeNet 最终删除工具")
    logger.info("=" * 50)
    
    # 检查管理员权限
    if not check_admin_privileges():
        logger.warning("⚠️  建议以管理员权限运行此脚本以获得最佳效果")
    
    # 检查是否是重启后运行
    after_reboot = '--after-reboot' in sys.argv
    
    if after_reboot:
        logger.info("🔄 检测到重启后运行模式")
        success = immediate_delete_attempt()
        if success:
            logger.info("✅ 重启后删除操作成功完成！")
        else:
            logger.warning("❌ 重启后删除仍有部分失败")
        return
    
    # 正常运行模式
    print("\n选择操作模式:")
    print("1. 立即尝试删除")
    print("2. 创建重启后自动删除脚本")
    print("3. 两个都执行")
    print()
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            success = immediate_delete_attempt()
            if not success:
                print("\n立即删除失败，建议选择重启后删除")
                
        elif choice == "2":
            create_reboot_delete_script()
            print("\n✅ 重启删除脚本已创建！")
            print("请重启计算机，脚本将自动运行")
            
        elif choice == "3":
            success = immediate_delete_attempt()
            if not success:
                print("\n立即删除失败，正在创建重启后删除脚本...")
                create_reboot_delete_script()
                print("\n✅ 重启删除脚本已创建！")
                print("请重启计算机，脚本将自动运行")
            else:
                print("\n✅ 立即删除成功，无需重启！")
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n操作已取消")

if __name__ == "__main__":
    main()
