#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的EsafeNet文件删除工具
"""

import subprocess
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def force_delete_file(file_path):
    """
    强制删除单个文件
    """
    file_obj = Path(file_path)
    
    if not file_obj.exists():
        return True
    
    logger.info(f"正在删除文件: {file_obj.name}")
    
    # 方法1: 标准删除
    try:
        file_obj.unlink()
        logger.info(f"  ✓ 标准删除成功: {file_obj.name}")
        return True
    except Exception as e:
        logger.warning(f"  ✗ 标准删除失败: {e}")
    
    # 方法2: 使用takeown和icacls获取权限
    try:
        logger.info(f"  尝试获取文件所有权: {file_obj.name}")
        
        # 获取文件所有权
        subprocess.run(['takeown', '/f', str(file_path)], 
                      capture_output=True, timeout=30)
        
        # 设置完全控制权限
        subprocess.run(['icacls', str(file_path), '/grant', 'administrators:F'], 
                      capture_output=True, timeout=30)
        
        # 移除只读、系统、隐藏属性
        subprocess.run(['attrib', '-r', '-s', '-h', str(file_path)], 
                      capture_output=True)
        
        # 再次尝试删除
        file_obj.unlink()
        logger.info(f"  ✓ 获取权限后删除成功: {file_obj.name}")
        return True
    except Exception as e:
        logger.warning(f"  ✗ 获取权限后删除失败: {e}")
    
    # 方法3: 使用del命令
    try:
        subprocess.run(['del', '/f', '/q', str(file_path)], 
                      shell=True, check=True, capture_output=True)
        
        # 检查文件是否还存在
        if not file_obj.exists():
            logger.info(f"  ✓ del命令删除成功: {file_obj.name}")
            return True
    except Exception as e:
        logger.warning(f"  ✗ del命令删除失败: {e}")
    
    # 方法4: 使用PowerShell
    try:
        ps_command = f'Remove-Item -Path "{file_path}" -Force -ErrorAction SilentlyContinue'
        subprocess.run(['powershell', '-Command', ps_command], 
                      capture_output=True, timeout=30)
        
        # 检查文件是否还存在
        if not file_obj.exists():
            logger.info(f"  ✓ PowerShell删除成功: {file_obj.name}")
            return True
    except Exception as e:
        logger.warning(f"  ✗ PowerShell删除失败: {e}")
    
    logger.error(f"  ✗ 所有删除方法都失败: {file_obj.name}")
    return False

def main():
    target_directory = r"C:\Program Files\EsafeNet\Cobra DocGuard Client"
    
    logger.info("=== EsafeNet 简单删除工具 ===")
    logger.info(f"目标目录: {target_directory}")
    
    target_dir = Path(target_directory)
    
    if not target_dir.exists():
        logger.info("目标目录不存在，无需删除")
        return
    
    # 停止资源管理器
    logger.info("正在停止资源管理器...")
    try:
        subprocess.run(['taskkill', '/f', '/im', 'explorer.exe'], 
                      capture_output=True)
        time.sleep(2)
    except Exception as e:
        logger.warning(f"停止资源管理器失败: {e}")
    
    # 停止相关进程
    logger.info("正在停止相关进程...")
    processes = ['CommonService.exe', 'HnCommonService.exe']
    for process in processes:
        try:
            subprocess.run(['taskkill', '/f', '/im', process], 
                          capture_output=True)
            logger.info(f"已停止进程: {process}")
        except Exception:
            pass
    
    time.sleep(3)
    
    # 获取所有文件
    files = list(target_dir.iterdir())
    
    if not files:
        logger.info("目录为空，无需删除")
    else:
        logger.info(f"找到 {len(files)} 个文件/目录需要删除")
        
        deleted_count = 0
        failed_files = []
        
        for file_path in files:
            if force_delete_file(file_path):
                deleted_count += 1
            else:
                failed_files.append(str(file_path))
        
        logger.info(f"删除结果: 成功 {deleted_count} 个，失败 {len(failed_files)} 个")
        
        if failed_files:
            logger.warning("删除失败的文件:")
            for failed_file in failed_files:
                logger.warning(f"  - {failed_file}")
    
    # 重启资源管理器
    logger.info("正在重启资源管理器...")
    try:
        subprocess.Popen(['explorer.exe'])
        time.sleep(2)
    except Exception as e:
        logger.warning(f"重启资源管理器失败: {e}")
    
    # 最终检查
    remaining_files = list(target_dir.iterdir()) if target_dir.exists() else []
    if remaining_files:
        logger.warning(f"仍有 {len(remaining_files)} 个文件未删除")
        logger.info("建议:")
        logger.info("1. 以管理员权限运行此脚本")
        logger.info("2. 重启计算机后再次运行")
        logger.info("3. 在安全模式下运行")
    else:
        logger.info("✅ 所有文件删除成功！")

if __name__ == "__main__":
    main()
