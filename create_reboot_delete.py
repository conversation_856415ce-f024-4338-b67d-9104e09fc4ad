#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建重启后自动删除EsafeNet文件的脚本
"""

from pathlib import Path


def create_reboot_delete_script():
    """
    创建重启后自动删除的脚本
    """

    # 创建批处理脚本内容
    bat_content = """@echo off
echo ========================================
echo EsafeNet 重启后删除工具
echo ========================================
echo.

set "TARGET_DIR=C:\\Program Files\\EsafeNet\\Cobra DocGuard Client"

echo 正在删除EsafeNet文件...
echo 目标目录: %TARGET_DIR%
echo.

:: 等待系统完全启动
timeout /t 10 /nobreak >nul

:: 删除文件
if exist "%TARGET_DIR%\\ProcessCtrl64.sys" (
    echo 正在删除 ProcessCtrl64.sys...
    
    :: 获取所有权
    takeown /f "%TARGET_DIR%\\ProcessCtrl64.sys" /a >nul 2>&1
    
    :: 设置权限
    icacls "%TARGET_DIR%\\ProcessCtrl64.sys" /grant administrators:F >nul 2>&1
    
    :: 移除属性
    attrib -r -s -h "%TARGET_DIR%\\ProcessCtrl64.sys" >nul 2>&1
    
    :: 删除文件
    del /f /q "%TARGET_DIR%\\ProcessCtrl64.sys" >nul 2>&1
    
    if exist "%TARGET_DIR%\\ProcessCtrl64.sys" (
        echo 删除失败: ProcessCtrl64.sys
    ) else (
        echo 删除成功: ProcessCtrl64.sys
    )
) else (
    echo ProcessCtrl64.sys 不存在或已删除
)

:: 删除其他可能的文件
for %%f in ("%TARGET_DIR%\\*.*") do (
    echo 正在删除: %%~nxf
    takeown /f "%%f" /a >nul 2>&1
    icacls "%%f" /grant administrators:F >nul 2>&1
    attrib -r -s -h "%%f" >nul 2>&1
    del /f /q "%%f" >nul 2>&1
    
    if exist "%%f" (
        echo   失败: %%~nxf
    ) else (
        echo   成功: %%~nxf
    )
)

:: 尝试删除目录
rmdir "%TARGET_DIR%" >nul 2>&1
if not exist "%TARGET_DIR%" (
    echo 目录删除成功
) else (
    echo 目录仍然存在
)

echo.
echo 删除操作完成
echo 按任意键关闭窗口...
pause >nul

:: 删除自身
del "%~f0" >nul 2>&1
"""

    # 获取启动文件夹路径
    startup_folder = (
        Path.home()
        / "AppData"
        / "Roaming"
        / "Microsoft"
        / "Windows"
        / "Start Menu"
        / "Programs"
        / "Startup"
    )

    # 确保启动文件夹存在
    startup_folder.mkdir(parents=True, exist_ok=True)

    # 创建批处理文件
    bat_file = startup_folder / "delete_esafenet_reboot.bat"

    try:
        with open(bat_file, "w", encoding="gbk") as f:
            f.write(bat_content)

        print(f"✅ 重启删除脚本已创建: {bat_file}")
        print("\n重启计算机后，脚本将自动运行并删除EsafeNet文件")
        print("脚本运行完成后会自动删除自身")

        return True

    except Exception as e:
        print(f"❌ 创建重启脚本失败: {e}")
        return False


def create_manual_delete_script():
    """
    创建手动运行的删除脚本
    """

    script_content = """@echo off
echo ========================================
echo EsafeNet 手动删除工具 (需要管理员权限)
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    echo 请右键点击此文件并选择"以管理员身份运行"
    pause
    exit /b 1
)

set "TARGET_DIR=C:\\Program Files\\EsafeNet\\Cobra DocGuard Client"

echo 目标目录: %TARGET_DIR%
echo.

if not exist "%TARGET_DIR%" (
    echo 目录不存在，无需删除
    pause
    exit /b 0
)

echo 正在列出目录内容:
dir "%TARGET_DIR%"
echo.

echo 按任意键开始删除操作...
pause
echo.

:: 停止相关服务和进程
echo 正在停止相关服务和进程...
net stop "CommonService" /y >nul 2>&1
net stop "HnCommonService" /y >nul 2>&1
taskkill /f /im CommonService.exe >nul 2>&1
taskkill /f /im HnCommonService.exe >nul 2>&1
taskkill /f /im explorer.exe >nul 2>&1

timeout /t 3 >nul

echo 正在删除文件...

:: 处理每个文件
for %%f in ("%TARGET_DIR%\\*.*") do (
    echo 正在处理: %%~nxf
    
    :: 获取所有权
    takeown /f "%%f" /a >nul 2>&1
    
    :: 设置权限
    icacls "%%f" /grant administrators:F >nul 2>&1
    
    :: 移除属性
    attrib -r -s -h "%%f" >nul 2>&1
    
    :: 删除文件
    del /f /q "%%f" >nul 2>&1
    
    if exist "%%f" (
        echo   失败: %%~nxf
    ) else (
        echo   成功: %%~nxf
    )
)

:: 重启资源管理器
start explorer.exe

echo.
echo 删除操作完成
echo.

:: 检查结果
if exist "%TARGET_DIR%\\*.*" (
    echo 仍有文件未删除:
    dir "%TARGET_DIR%"
    echo.
    echo 建议重启计算机后再次运行此脚本
) else (
    echo 所有文件删除成功！
)

pause
"""

    try:
        with open("delete_esafenet_manual.bat", "w", encoding="gbk") as f:
            f.write(script_content)

        print("✅ 手动删除脚本已创建: delete_esafenet_manual.bat")
        print("请右键点击此文件并选择'以管理员身份运行'")

        return True

    except Exception as e:
        print(f"❌ 创建手动脚本失败: {e}")
        return False


def main():
    print("=== EsafeNet 删除脚本创建工具 ===")
    print()
    print("选择要创建的脚本类型:")
    print("1. 重启后自动删除脚本")
    print("2. 手动删除脚本 (需要管理员权限)")
    print("3. 两个都创建")
    print()

    try:
        choice = input("请选择 (1/2/3): ").strip()

        if choice == "1":
            create_reboot_delete_script()
        elif choice == "2":
            create_manual_delete_script()
        elif choice == "3":
            create_reboot_delete_script()
            print()
            create_manual_delete_script()
        else:
            print("无效选择")
            return

    except KeyboardInterrupt:
        print("\n操作已取消")
        return


if __name__ == "__main__":
    main()
