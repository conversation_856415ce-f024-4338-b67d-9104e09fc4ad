#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EsafeNet ProcessCtrl64.sys 最终删除工具
解决编码问题，使用Python实现
"""

import subprocess
import logging
import time
import sys
import os
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_admin_privileges():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False

def stop_driver(driver_name):
    """停止驱动"""
    logger.info(f"正在停止驱动: {driver_name}")
    
    try:
        result = subprocess.run(
            ["sc", "stop", driver_name],
            capture_output=True, text=True, timeout=30
        )
        
        if result.returncode == 0:
            logger.info(f"✅ 驱动已停止: {driver_name}")
            return True
        else:
            logger.warning(f"⚠️  驱动停止失败或不存在: {driver_name}")
            return False
            
    except Exception as e:
        logger.error(f"停止驱动时出错 {driver_name}: {e}")
        return False

def disable_driver(driver_name):
    """禁用驱动自动启动"""
    logger.info(f"正在禁用驱动自动启动: {driver_name}")
    
    try:
        result = subprocess.run(
            ["sc", "config", driver_name, "start=", "disabled"],
            capture_output=True, text=True, timeout=30
        )
        
        if result.returncode == 0:
            logger.info(f"✅ 驱动已禁用: {driver_name}")
            return True
        else:
            logger.warning(f"⚠️  驱动禁用失败: {driver_name}")
            return False
            
    except Exception as e:
        logger.error(f"禁用驱动时出错 {driver_name}: {e}")
        return False

def clean_registry_items():
    """清理注册表启动项"""
    logger.info("正在清理注册表启动项...")
    
    registry_paths = [
        r"HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
        r"HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run"
    ]
    
    startup_items = ["CDGRegedit", "NsEPSTray", "CDGUPGRADE"]
    
    cleaned_count = 0
    
    for reg_path in registry_paths:
        for item_name in startup_items:
            try:
                result = subprocess.run(
                    ["reg", "delete", reg_path, "/v", item_name, "/f"],
                    capture_output=True, text=True, timeout=30
                )
                
                if result.returncode == 0:
                    logger.info(f"✅ 已删除注册表项: {reg_path}\\{item_name}")
                    cleaned_count += 1
                    
            except Exception as e:
                logger.warning(f"删除注册表项失败 {reg_path}\\{item_name}: {e}")
    
    logger.info(f"✅ 注册表启动项清理完成，共清理 {cleaned_count} 项")
    return cleaned_count

def delete_file(file_path):
    """删除文件"""
    logger.info(f"正在尝试删除文件: {file_path}")
    
    # 方法1: 获取所有权并删除
    try:
        subprocess.run(["takeown", "/f", str(file_path), "/a"], 
                      capture_output=True, timeout=30)
        subprocess.run(["icacls", str(file_path), "/grant", "administrators:F"], 
                      capture_output=True, timeout=30)
        subprocess.run(["attrib", "-r", "-s", "-h", str(file_path)], 
                      capture_output=True)
        subprocess.run(["del", "/f", "/q", str(file_path)], 
                      shell=True, capture_output=True)
        
        if not Path(file_path).exists():
            logger.info("✅ 文件删除成功！")
            return True
            
    except Exception as e:
        logger.warning(f"标准方法删除失败: {e}")
    
    # 方法2: 使用PowerShell
    try:
        ps_command = f'Remove-Item -Path "{file_path}" -Force -ErrorAction SilentlyContinue'
        subprocess.run(["powershell", "-Command", ps_command], 
                      capture_output=True, timeout=30)
        
        if not Path(file_path).exists():
            logger.info("✅ PowerShell方法删除成功！")
            return True
            
    except Exception as e:
        logger.warning(f"PowerShell方法删除失败: {e}")
    
    logger.error("❌ 所有删除方法都失败")
    return False

def create_reboot_script(target_file):
    """创建重启后删除脚本"""
    logger.info("正在创建重启后删除脚本...")
    
    startup_folder = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
    startup_folder.mkdir(parents=True, exist_ok=True)
    
    script_path = startup_folder / "delete_processctrl_final.bat"
    
    script_content = f'''@echo off
echo Deleting ProcessCtrl64.sys after reboot...
timeout /t 15 /nobreak >nul

takeown /f "{target_file}" /a >nul 2>&1
icacls "{target_file}" /grant administrators:F >nul 2>&1
attrib -r -s -h "{target_file}" >nul 2>&1
del /f /q "{target_file}" >nul 2>&1

if not exist "{target_file}" (
    echo SUCCESS: ProcessCtrl64.sys deleted successfully!
) else (
    echo ERROR: ProcessCtrl64.sys still cannot be deleted
)

timeout /t 5 >nul
del "%~f0" >nul 2>&1
'''

    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        logger.info(f"✅ 重启后删除脚本已创建: {script_path}")
        return True
        
    except Exception as e:
        logger.error(f"创建重启脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("EsafeNet ProcessCtrl64.sys 最终删除工具")
    print("=" * 60)
    print()
    
    # 检查管理员权限
    if not check_admin_privileges():
        logger.error("❌ 需要管理员权限运行此脚本")
        logger.info("请右键点击命令提示符并选择'以管理员身份运行'")
        input("按任意键退出...")
        return
    
    logger.info("✅ 检测到管理员权限")
    
    target_file = r"C:\Program Files\EsafeNet\Cobra DocGuard Client\ProcessCtrl64.sys"
    
    logger.info(f"目标文件: {target_file}")
    
    if not Path(target_file).exists():
        logger.info("✅ ProcessCtrl64.sys 文件不存在，可能已被删除")
        input("按任意键退出...")
        return
    
    print("\n解决方案步骤:")
    print("1. 停止ProcessCtrl驱动")
    print("2. 禁用驱动自动启动")
    print("3. 清理注册表启动项")
    print("4. 删除文件")
    print("5. 如果失败，创建重启后删除脚本")
    print()
    
    input("按任意键开始执行...")
    print()
    
    # 第1步：停止驱动
    logger.info("=" * 40)
    logger.info("第1步: 停止ProcessCtrl驱动")
    logger.info("=" * 40)
    
    stop_driver("ProcessCtrl")
    stop_driver("clasLock")
    time.sleep(3)
    
    # 第2步：禁用驱动
    logger.info("=" * 40)
    logger.info("第2步: 禁用驱动自动启动")
    logger.info("=" * 40)
    
    disable_driver("ProcessCtrl")
    disable_driver("clasLock")
    
    # 第3步：清理注册表
    logger.info("=" * 40)
    logger.info("第3步: 清理注册表启动项")
    logger.info("=" * 40)
    
    clean_registry_items()
    
    # 第4步：删除文件
    logger.info("=" * 40)
    logger.info("第4步: 尝试删除文件")
    logger.info("=" * 40)
    
    if delete_file(target_file):
        print()
        logger.info("=" * 40)
        logger.info("🎉 任务完成！")
        logger.info("=" * 40)
        logger.info("✅ ProcessCtrl64.sys 文件已成功删除")
        logger.info("✅ EsafeNet驱动已停止并禁用")
        logger.info("✅ 注册表启动项已清理")
    else:
        # 第5步：创建重启脚本
        logger.info("=" * 40)
        logger.info("第5步: 创建重启后删除脚本")
        logger.info("=" * 40)
        
        logger.warning("文件仍然被占用，正在创建重启后删除脚本...")
        
        if create_reboot_script(target_file):
            logger.info("✅ 重启后删除脚本已创建")
            logger.info("🔄 请重启计算机，脚本将自动运行并删除文件")
        else:
            logger.error("❌ 创建重启脚本失败")
    
    print()
    input("按任意键退出...")

if __name__ == "__main__":
    main()
