#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门停止EsafeNet驱动的脚本
"""

import subprocess
import logging
import time
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_admin_privileges():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False

def stop_driver(driver_name):
    """停止单个驱动"""
    logger.info(f"正在停止驱动: {driver_name}")
    
    try:
        # 使用sc stop停止驱动
        stop_result = subprocess.run(
            ["sc", "stop", driver_name],
            capture_output=True,
            text=True,
            timeout=30,
            encoding="gbk",
            errors="ignore",
        )
        
        if stop_result.returncode == 0:
            logger.info(f"✅ 驱动已停止: {driver_name}")
            return True
        else:
            logger.warning(f"❌ 停止驱动失败: {driver_name}")
            logger.warning(f"错误信息: {stop_result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"停止驱动时出错 {driver_name}: {e}")
        return False

def disable_driver(driver_name):
    """禁用驱动的自动启动"""
    logger.info(f"正在禁用驱动自动启动: {driver_name}")
    
    try:
        # 使用sc config设置驱动为禁用
        config_result = subprocess.run(
            ["sc", "config", driver_name, "start=", "disabled"],
            capture_output=True,
            text=True,
            timeout=30,
            encoding="gbk",
            errors="ignore",
        )
        
        if config_result.returncode == 0:
            logger.info(f"✅ 驱动已禁用: {driver_name}")
            return True
        else:
            logger.warning(f"❌ 禁用驱动失败: {driver_name}")
            logger.warning(f"错误信息: {config_result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"禁用驱动时出错 {driver_name}: {e}")
        return False

def check_driver_status(driver_name):
    """检查驱动状态"""
    try:
        query_result = subprocess.run(
            ["sc", "query", driver_name],
            capture_output=True,
            text=True,
            timeout=30,
            encoding="gbk",
            errors="ignore",
        )
        
        if query_result.returncode == 0:
            output = query_result.stdout
            if "RUNNING" in output:
                return "运行中"
            elif "STOPPED" in output:
                return "已停止"
            else:
                return "未知状态"
        else:
            return "不存在"
            
    except Exception as e:
        logger.error(f"检查驱动状态失败 {driver_name}: {e}")
        return "检查失败"

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("EsafeNet 驱动停止工具")
    logger.info("=" * 60)
    
    # 检查管理员权限
    if not check_admin_privileges():
        logger.error("❌ 需要管理员权限运行此脚本")
        logger.info("请右键点击命令提示符并选择'以管理员身份运行'")
        input("按任意键退出...")
        return
    
    logger.info("✅ 检测到管理员权限")
    
    # EsafeNet驱动名称
    driver_names = [
        "ProcessCtrl",
        "clasLock"
    ]
    
    # 检查驱动状态
    logger.info("\n🔍 检查驱动当前状态:")
    for driver_name in driver_names:
        status = check_driver_status(driver_name)
        logger.info(f"  {driver_name}: {status}")
    
    print("\n选择操作:")
    print("1. 停止驱动")
    print("2. 禁用驱动自动启动")
    print("3. 停止并禁用驱动")
    print("4. 仅检查状态")
    
    try:
        choice = input("\n请选择 (1/2/3/4): ").strip()
        
        if choice == "1":
            logger.info("\n🛑 正在停止驱动...")
            for driver_name in driver_names:
                stop_driver(driver_name)
                
        elif choice == "2":
            logger.info("\n🚫 正在禁用驱动自动启动...")
            for driver_name in driver_names:
                disable_driver(driver_name)
                
        elif choice == "3":
            logger.info("\n🛑 正在停止并禁用驱动...")
            for driver_name in driver_names:
                stop_driver(driver_name)
                time.sleep(1)
                disable_driver(driver_name)
                
        elif choice == "4":
            logger.info("\n✅ 状态检查完成")
            
        else:
            logger.warning("无效选择")
            return
        
        # 再次检查状态
        logger.info("\n🔍 操作后驱动状态:")
        for driver_name in driver_names:
            status = check_driver_status(driver_name)
            logger.info(f"  {driver_name}: {status}")
        
        logger.info("\n✅ 操作完成！")
        logger.info("现在可以尝试删除ProcessCtrl64.sys文件")
        
    except KeyboardInterrupt:
        logger.info("\n操作已取消")

if __name__ == "__main__":
    main()
