#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：重启资源管理器并删除指定目录下的文件
作者：GitHub Copilot
日期：2025-07-18
"""

import logging
import re
import shutil
import subprocess
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("delete_operation.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


def kill_explorer():
    """
    终止资源管理器进程
    """
    try:
        logger.info("正在终止资源管理器进程...")
        subprocess.run(
            ["taskkill", "/f", "/im", "explorer.exe"],
            check=True,
            capture_output=True,
            text=True,
        )
        logger.info("资源管理器进程已终止")
        time.sleep(1)  # 等待进程完全终止
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"终止资源管理器失败: {e}")
        return False


def start_explorer():
    """
    启动资源管理器进程
    """
    try:
        logger.info("正在启动资源管理器...")
        subprocess.Popen(["explorer.exe"])
        logger.info("资源管理器已启动")
        time.sleep(3)  # 等待资源管理器完全启动
        return True
    except Exception as e:
        logger.error(f"启动资源管理器失败: {e}")
        return False


def stop_esafenet_services():
    """
    停止EsafeNet相关的Windows服务
    """
    logger.info("正在检查并停止EsafeNet相关服务...")

    # 可能的EsafeNet服务名称模式
    service_patterns = [
        "EsafeNet",
        "Cobra",
        "DocGuard",
        "CommonService",
        "HnCommonService",
    ]

    stopped_services = []

    try:
        # 获取所有服务列表，使用中文Windows的编码
        result = subprocess.run(
            ["sc", "query"],
            capture_output=True,
            text=True,
            encoding="gbk",
            errors="ignore",
        )

        if result.returncode == 0 and result.stdout:
            services_output = result.stdout

            # 查找匹配的服务
            for pattern in service_patterns:
                # 使用正则表达式查找服务名
                matches = re.findall(
                    r"SERVICE_NAME:\s*(\S*" + pattern + r"\S*)",
                    services_output,
                    re.IGNORECASE,
                )

                for service_name in matches:
                    try:
                        logger.info(f"尝试停止服务: {service_name}")

                        # 先尝试正常停止
                        stop_result = subprocess.run(
                            ["sc", "stop", service_name],
                            capture_output=True,
                            text=True,
                            timeout=30,
                            encoding="gbk",
                            errors="ignore",
                        )

                        if stop_result.returncode == 0:
                            logger.info(f"服务已停止: {service_name}")
                            stopped_services.append(service_name)
                        else:
                            # 如果正常停止失败，尝试强制停止
                            logger.warning(
                                f"正常停止失败，尝试强制停止服务: {service_name}"
                            )

                            # 使用net stop命令
                            net_result = subprocess.run(
                                ["net", "stop", service_name, "/y"],
                                capture_output=True,
                                text=True,
                                timeout=30,
                                encoding="gbk",
                                errors="ignore",
                            )

                            if net_result.returncode == 0:
                                logger.info(f"服务已强制停止: {service_name}")
                                stopped_services.append(service_name)
                            else:
                                logger.warning(f"强制停止服务也失败: {service_name}")

                    except subprocess.TimeoutExpired:
                        logger.warning(f"停止服务超时: {service_name}")
                    except Exception as e:
                        logger.error(f"停止服务时出错 {service_name}: {e}")

        if stopped_services:
            logger.info(f"已停止 {len(stopped_services)} 个服务，等待10秒...")
            time.sleep(10)
        else:
            logger.info("未找到需要停止的EsafeNet相关服务")

        return stopped_services

    except Exception as e:
        logger.error(f"检查服务时出错: {e}")
        return []


def kill_processes_using_files(target_path):
    """
    终止可能占用目标目录文件的进程
    """
    logger.info("正在检查并终止占用文件的进程...")

    killed_processes = []

    try:
        # 使用tasklist查找可能相关的进程，使用中文Windows的编码
        process_patterns = [
            "CommonService.exe",
            "HnCommonService.exe",
            "EsafeNet",
            "Cobra",
            "DocGuard",
        ]

        result = subprocess.run(
            ["tasklist"],
            capture_output=True,
            text=True,
            encoding="gbk",
            errors="ignore",
        )

        if result.returncode == 0 and result.stdout:
            processes_output = result.stdout

            for pattern in process_patterns:
                # 查找匹配的进程
                lines = processes_output.split("\n")
                for line in lines:
                    if pattern.lower() in line.lower():
                        # 提取进程名（第一列）
                        parts = line.split()
                        if parts:
                            process_name = parts[0]
                            try:
                                logger.info(f"尝试终止进程: {process_name}")
                                kill_result = subprocess.run(
                                    ["taskkill", "/f", "/im", process_name],
                                    capture_output=True,
                                    text=True,
                                    timeout=30,
                                    encoding="gbk",
                                    errors="ignore",
                                )

                                if kill_result.returncode == 0:
                                    logger.info(f"进程已终止: {process_name}")
                                    killed_processes.append(process_name)
                                else:
                                    # 如果失败，尝试通过PID强制终止
                                    logger.warning(
                                        f"正常终止失败，尝试通过PID强制终止: {process_name}"
                                    )

                                    # 获取进程PID
                                    if len(parts) >= 2:
                                        try:
                                            pid = parts[1]
                                            pid_kill_result = subprocess.run(
                                                ["taskkill", "/f", "/pid", pid],
                                                capture_output=True,
                                                text=True,
                                                timeout=30,
                                                encoding="gbk",
                                                errors="ignore",
                                            )

                                            if pid_kill_result.returncode == 0:
                                                logger.info(
                                                    f"通过PID强制终止成功: {process_name} (PID: {pid})"
                                                )
                                                killed_processes.append(process_name)
                                            else:
                                                logger.warning(
                                                    f"通过PID强制终止也失败: {process_name}"
                                                )
                                        except Exception as e:
                                            logger.error(f"通过PID终止进程时出错: {e}")
                                    else:
                                        logger.warning(
                                            f"无法获取进程PID: {process_name}"
                                        )

                            except subprocess.TimeoutExpired:
                                logger.warning(f"终止进程超时: {process_name}")
                            except Exception as e:
                                logger.error(f"终止进程时出错 {process_name}: {e}")

        if killed_processes:
            logger.info(f"已终止 {len(killed_processes)} 个进程，等待5秒...")
            time.sleep(5)
        else:
            logger.info("未找到需要终止的相关进程")

        return killed_processes

    except Exception as e:
        logger.error(f"检查进程时出错: {e}")
        return []


def force_delete_file(file_path):
    """
    强制删除文件的多种方法
    """
    file_obj = Path(file_path)

    # 方法1: 标准删除
    try:
        file_obj.unlink()
        return True
    except:
        pass

    # 方法2: 使用del命令
    try:
        subprocess.run(
            ["del", "/f", "/q", str(file_path)],
            shell=True,
            check=True,
            capture_output=True,
        )
        return True
    except Exception:
        pass

    # 方法3: 使用attrib移除只读属性后删除
    try:
        subprocess.run(
            ["attrib", "-r", "-s", "-h", str(file_path)], capture_output=True
        )
        file_obj.unlink()
        return True
    except Exception:
        pass

    # 方法4: 对于.sys文件，尝试使用takeown和icacls获取权限
    if file_obj.suffix.lower() == ".sys":
        try:
            logger.info(f"检测到系统驱动文件，尝试获取权限: {file_obj.name}")

            # 获取文件所有权
            subprocess.run(
                ["takeown", "/f", str(file_path)], capture_output=True, timeout=30
            )

            # 设置完全控制权限
            subprocess.run(
                ["icacls", str(file_path), "/grant", "administrators:F"],
                capture_output=True,
                timeout=30,
            )

            # 再次尝试删除
            file_obj.unlink()
            return True
        except Exception as e:
            logger.warning(f"获取系统文件权限失败: {e}")

    # 方法5: 使用PowerShell强制删除
    try:
        ps_command = (
            f'Remove-Item -Path "{file_path}" -Force -ErrorAction SilentlyContinue'
        )
        subprocess.run(
            ["powershell", "-Command", ps_command], capture_output=True, timeout=30
        )

        # 检查文件是否还存在
        if not file_obj.exists():
            return True
    except Exception:
        pass

    return False


def delete_directory_contents_enhanced(target_path):
    """
    增强版删除目录内容函数，包含重试和强制删除机制
    """
    target_dir = Path(target_path)

    if not target_dir.exists():
        logger.warning(f"目标目录不存在: {target_path}")
        return False

    if not target_dir.is_dir():
        logger.error(f"指定路径不是目录: {target_path}")
        return False

    # 第一步：停止相关服务
    stopped_services = stop_esafenet_services()

    # 第二步：终止相关进程
    killed_processes = kill_processes_using_files(target_path)

    # 第三步：尝试删除文件
    try:
        logger.info(f"开始删除目录内容: {target_path}")

        items = list(target_dir.iterdir())

        if not items:
            logger.info("目录为空，无需删除")
            return True

        deleted_count = 0
        failed_items = []

        for item in items:
            try:
                if item.is_file():
                    # 对于文件，先尝试标准删除，失败则使用强制删除
                    if force_delete_file(item):
                        logger.info(f"已删除文件: {item.name}")
                        deleted_count += 1
                    else:
                        logger.error(f"无法删除文件: {item.name}")
                        failed_items.append(str(item))

                elif item.is_dir():
                    # 删除目录及其内容
                    shutil.rmtree(item, ignore_errors=False)
                    logger.info(f"已删除目录: {item.name}")
                    deleted_count += 1

            except PermissionError:
                logger.error(f"权限不足，无法删除: {item.name}")
                failed_items.append(str(item))
            except FileNotFoundError:
                logger.warning(f"文件/目录不存在: {item.name}")
            except Exception as e:
                logger.error(f"删除失败 {item.name}: {e}")
                failed_items.append(str(item))

        # 如果还有失败的项目，进行重试
        if failed_items and (stopped_services or killed_processes):
            logger.info("有服务或进程被停止，尝试重新删除失败的文件...")
            time.sleep(2)

            retry_failed = []
            for item_path in failed_items[:]:  # 创建副本来避免修改正在迭代的列表
                item = Path(item_path)
                if item.exists():
                    try:
                        if item.is_file():
                            if force_delete_file(item):
                                logger.info(f"重试成功删除文件: {item.name}")
                                deleted_count += 1
                                failed_items.remove(item_path)
                            else:
                                retry_failed.append(item_path)
                        elif item.is_dir():
                            shutil.rmtree(item, ignore_errors=False)
                            logger.info(f"重试成功删除目录: {item.name}")
                            deleted_count += 1
                            failed_items.remove(item_path)
                    except Exception as e:
                        logger.error(f"重试删除失败 {item.name}: {e}")
                        retry_failed.append(item_path)
                else:
                    # 文件已经不存在了
                    failed_items.remove(item_path)

        logger.info(f"删除操作完成。成功删除 {deleted_count} 个项目")

        if failed_items:
            logger.warning(f"以下 {len(failed_items)} 个项目删除失败:")
            for item in failed_items:
                logger.warning(f"  - {item}")

            # 提供手动解决建议
            logger.info("建议手动解决方案:")
            logger.info("1. 重启计算机后再次运行脚本")
            logger.info("2. 在安全模式下运行脚本")
            logger.info("3. 使用专门的文件解锁工具")

            return False

        return True

    except Exception as e:
        logger.error(f"删除目录内容时发生错误: {e}")
        return False


def restart_explorer_and_delete(target_path):
    """
    重启资源管理器并删除指定目录内容的主函数

    Args:
        target_path (str): 要删除内容的目录路径
    """
    logger.info("=" * 50)
    logger.info("开始执行重启资源管理器并删除文件操作")
    logger.info("=" * 50)

    # 步骤1: 终止资源管理器
    if not kill_explorer():
        logger.error("无法终止资源管理器，操作中止")
        return False

    try:
        # 步骤2: 删除目录内容（使用增强版函数）
        success = delete_directory_contents_enhanced(target_path)

        # 步骤3: 重启资源管理器
        if not start_explorer():
            logger.error("无法启动资源管理器")

        if success:
            logger.info("操作成功完成")
        else:
            logger.warning("操作完成，但部分文件删除失败")

        return success

    except Exception as e:
        logger.error(f"操作过程中发生未预期的错误: {e}")
        # 确保资源管理器重新启动
        start_explorer()
        return False


def create_reboot_script():
    """
    创建重启后自动运行的脚本
    """
    script_content = """@echo off
echo 正在重启后清理EsafeNet文件...
cd /d "%~dp0"
python delete.py --after-reboot
pause
"""

    try:
        with open("delete_after_reboot.bat", "w", encoding="utf-8") as f:
            f.write(script_content)

        # 添加到启动项
        startup_path = (
            Path.home()
            / "AppData"
            / "Roaming"
            / "Microsoft"
            / "Windows"
            / "Start Menu"
            / "Programs"
            / "Startup"
        )
        startup_script = startup_path / "delete_after_reboot.bat"

        import shutil

        shutil.copy2("delete_after_reboot.bat", startup_script)

        logger.info("已创建重启后自动运行脚本")
        return True
    except Exception as e:
        logger.error(f"创建重启脚本失败: {e}")
        return False


def main():
    """
    主函数
    """
    import sys

    target_directory = r"C:\Program Files\EsafeNet\Cobra DocGuard Client"

    # 检查是否是重启后运行
    after_reboot = "--after-reboot" in sys.argv

    # 检查是否以管理员权限运行
    try:
        # 尝试写入系统目录来检查权限
        import ctypes

        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            logger.warning("建议以管理员权限运行此脚本以确保删除操作成功")
    except Exception:
        logger.warning("无法检查管理员权限")

    if after_reboot:
        logger.info("检测到重启后运行模式，直接执行删除操作")
        success = restart_explorer_and_delete(target_directory)

        # 清理启动项中的脚本
        try:
            startup_path = (
                Path.home()
                / "AppData"
                / "Roaming"
                / "Microsoft"
                / "Windows"
                / "Start Menu"
                / "Programs"
                / "Startup"
            )
            startup_script = startup_path / "delete_after_reboot.bat"
            if startup_script.exists():
                startup_script.unlink()
                logger.info("已清理启动项中的重启脚本")
        except Exception as e:
            logger.warning(f"清理启动脚本失败: {e}")

        if success:
            print("\n✅ 重启后删除操作成功完成！")
        else:
            print("\n❌ 重启后删除操作完成，但遇到了一些问题")
        return

    # 正常运行模式
    # 确认操作
    print("\n即将执行以下操作:")
    print("1. 重启Windows资源管理器")
    print(f"2. 删除目录下的所有文件: {target_directory}")
    print("3. 如果删除失败，可选择创建重启后自动删除脚本")
    print("\n注意: 此操作不可逆，请确认后继续")

    try:
        confirm = input("\n是否继续? (y/N): ").strip().lower()
        if confirm not in ["y", "yes", "是"]:
            logger.info("操作已取消")
            return
    except KeyboardInterrupt:
        logger.info("\n操作已取消")
        return

    # 执行操作
    success = restart_explorer_and_delete(target_directory)

    if success:
        print("\n✅ 操作成功完成！")
    else:
        print("\n❌ 操作完成，但遇到了一些问题，请查看日志了解详情")

        # 如果删除失败，询问是否创建重启脚本
        try:
            reboot_confirm = (
                input("\n是否创建重启后自动删除脚本? (y/N): ").strip().lower()
            )
            if reboot_confirm in ["y", "yes", "是"]:
                if create_reboot_script():
                    print("\n✅ 重启脚本已创建！请重启计算机后脚本将自动运行")
                    print("重启后脚本将在系统启动时自动执行删除操作")
                else:
                    print("\n❌ 创建重启脚本失败")
        except KeyboardInterrupt:
            print("\n操作已取消")


if __name__ == "__main__":
    main()
