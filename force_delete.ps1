# PowerShell脚本：强力删除EsafeNet文件
# 需要以管理员权限运行

param(
    [string]$TargetPath = "C:\Program Files\EsafeNet\Cobra DocGuard Client"
)

Write-Host "=== EsafeNet 强力删除工具 ===" -ForegroundColor Green
Write-Host "目标目录: $TargetPath" -ForegroundColor Yellow

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-NOT $isAdmin) {
    Write-Host "错误: 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell并选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 停止资源管理器
Write-Host "正在停止资源管理器..." -ForegroundColor Yellow
Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

# 停止相关服务
Write-Host "正在停止相关服务..." -ForegroundColor Yellow
$services = @("CommonService", "HnCommonService", "EsafeNet*", "Cobra*", "DocGuard*")
foreach ($service in $services) {
    try {
        Get-Service -Name $service -ErrorAction SilentlyContinue | Stop-Service -Force -ErrorAction SilentlyContinue
        Write-Host "已停止服务: $service" -ForegroundColor Green
    }
    catch {
        Write-Host "服务不存在或已停止: $service" -ForegroundColor Gray
    }
}

# 终止相关进程
Write-Host "正在终止相关进程..." -ForegroundColor Yellow
$processes = @("CommonService*", "HnCommonService*", "EsafeNet*", "Cobra*", "DocGuard*")
foreach ($process in $processes) {
    try {
        Get-Process -Name $process -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
        Write-Host "已终止进程: $process" -ForegroundColor Green
    }
    catch {
        Write-Host "进程不存在或已终止: $process" -ForegroundColor Gray
    }
}

Start-Sleep -Seconds 3

# 检查目录是否存在
if (-not (Test-Path $TargetPath)) {
    Write-Host "目标目录不存在: $TargetPath" -ForegroundColor Red
    Start-Process "explorer.exe"
    pause
    exit 0
}

# 获取所有文件
$files = Get-ChildItem -Path $TargetPath -Force -ErrorAction SilentlyContinue

if ($files.Count -eq 0) {
    Write-Host "目录为空，无需删除" -ForegroundColor Green
    Start-Process "explorer.exe"
    pause
    exit 0
}

Write-Host "找到 $($files.Count) 个文件/目录需要删除" -ForegroundColor Yellow

$deletedCount = 0
$failedFiles = @()

foreach ($file in $files) {
    Write-Host "正在删除: $($file.Name)" -ForegroundColor Cyan
    
    try {
        # 方法1: 标准删除
        Remove-Item -Path $file.FullName -Force -Recurse -ErrorAction Stop
        Write-Host "  ✓ 标准删除成功" -ForegroundColor Green
        $deletedCount++
        continue
    }
    catch {
        Write-Host "  ✗ 标准删除失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    try {
        # 方法2: 获取所有权并删除
        Write-Host "  尝试获取文件所有权..." -ForegroundColor Yellow
        
        # 使用takeown获取所有权
        & takeown /f $file.FullName /a | Out-Null
        
        # 使用icacls设置权限
        & icacls $file.FullName /grant administrators:F | Out-Null
        
        # 移除只读、系统、隐藏属性
        $file.Attributes = $file.Attributes -band (-bnot [System.IO.FileAttributes]::ReadOnly)
        $file.Attributes = $file.Attributes -band (-bnot [System.IO.FileAttributes]::System)
        $file.Attributes = $file.Attributes -band (-bnot [System.IO.FileAttributes]::Hidden)
        
        # 再次尝试删除
        Remove-Item -Path $file.FullName -Force -Recurse -ErrorAction Stop
        Write-Host "  ✓ 获取权限后删除成功" -ForegroundColor Green
        $deletedCount++
        continue
    }
    catch {
        Write-Host "  ✗ 获取权限后删除失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    try {
        # 方法3: 使用.NET方法删除
        Write-Host "  尝试使用.NET方法删除..." -ForegroundColor Yellow
        [System.IO.File]::Delete($file.FullName)
        Write-Host "  ✓ .NET方法删除成功" -ForegroundColor Green
        $deletedCount++
        continue
    }
    catch {
        Write-Host "  ✗ .NET方法删除失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 如果所有方法都失败，添加到失败列表
    $failedFiles += $file.FullName
    Write-Host "  ✗ 所有删除方法都失败" -ForegroundColor Red
}

# 重启资源管理器
Write-Host "正在重启资源管理器..." -ForegroundColor Yellow
Start-Process "explorer.exe"
Start-Sleep -Seconds 2

# 显示结果
Write-Host "`n=== 删除结果 ===" -ForegroundColor Green
Write-Host "成功删除: $deletedCount 个文件/目录" -ForegroundColor Green

if ($failedFiles.Count -gt 0) {
    Write-Host "删除失败: $($failedFiles.Count) 个文件/目录" -ForegroundColor Red
    Write-Host "失败的文件:" -ForegroundColor Red
    foreach ($failedFile in $failedFiles) {
        Write-Host "  - $failedFile" -ForegroundColor Red
    }
    
    Write-Host "`n建议解决方案:" -ForegroundColor Yellow
    Write-Host "1. 重启计算机后再次运行此脚本" -ForegroundColor Yellow
    Write-Host "2. 在安全模式下运行此脚本" -ForegroundColor Yellow
    Write-Host "3. 使用专门的文件解锁工具" -ForegroundColor Yellow
}
else {
    Write-Host "所有文件删除成功！" -ForegroundColor Green
}

pause
