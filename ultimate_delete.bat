@echo off
echo ========================================
echo EsafeNet 终极删除工具
echo ========================================
echo.

set "TARGET_DIR=C:\Program Files\EsafeNet\Cobra DocGuard Client"

echo 目标目录: %TARGET_DIR%
echo.

:: 检查目录是否存在
if not exist "%TARGET_DIR%" (
    echo 目录不存在，无需删除
    pause
    exit /b 0
)

echo 正在列出目录内容...
dir "%TARGET_DIR%"
echo.

echo 按任意键开始删除操作...
pause
echo.

echo ========================================
echo 开始删除操作
echo ========================================

:: 停止资源管理器
echo 正在停止资源管理器...
taskkill /f /im explorer.exe >nul 2>&1
timeout /t 2 >nul

:: 停止相关服务
echo 正在停止相关服务...
net stop "CommonService" /y >nul 2>&1
net stop "HnCommonService" /y >nul 2>&1
sc stop CommonService >nul 2>&1
sc stop HnCommonService >nul 2>&1

:: 终止相关进程
echo 正在终止相关进程...
taskkill /f /im CommonService.exe >nul 2>&1
taskkill /f /im HnCommonService.exe >nul 2>&1

:: 等待进程完全终止
timeout /t 3 >nul

echo 正在尝试删除文件...

:: 方法1: 标准删除
echo 尝试方法1: 标准删除
del /f /q "%TARGET_DIR%\*.*" >nul 2>&1
if %errorlevel% equ 0 (
    echo 方法1成功
    goto :check_result
)

:: 方法2: 使用attrib移除属性后删除
echo 尝试方法2: 移除文件属性后删除
attrib -r -s -h "%TARGET_DIR%\*.*" >nul 2>&1
del /f /q "%TARGET_DIR%\*.*" >nul 2>&1
if %errorlevel% equ 0 (
    echo 方法2成功
    goto :check_result
)

:: 方法3: 使用takeown获取所有权
echo 尝试方法3: 获取文件所有权
takeown /f "%TARGET_DIR%\*.*" /a >nul 2>&1
icacls "%TARGET_DIR%\*.*" /grant administrators:F >nul 2>&1
del /f /q "%TARGET_DIR%\*.*" >nul 2>&1
if %errorlevel% equ 0 (
    echo 方法3成功
    goto :check_result
)

:: 方法4: 逐个文件处理
echo 尝试方法4: 逐个文件处理
for %%f in ("%TARGET_DIR%\*.*") do (
    echo 正在处理: %%~nxf
    
    :: 获取所有权
    takeown /f "%%f" /a >nul 2>&1
    
    :: 设置权限
    icacls "%%f" /grant administrators:F >nul 2>&1
    
    :: 移除属性
    attrib -r -s -h "%%f" >nul 2>&1
    
    :: 删除文件
    del /f /q "%%f" >nul 2>&1
    
    if exist "%%f" (
        echo   失败: %%~nxf
    ) else (
        echo   成功: %%~nxf
    )
)

:check_result
echo.
echo ========================================
echo 检查删除结果
echo ========================================

:: 重启资源管理器
echo 正在重启资源管理器...
start explorer.exe
timeout /t 2 >nul

:: 检查剩余文件
if exist "%TARGET_DIR%\*.*" (
    echo.
    echo 仍有文件未删除:
    dir "%TARGET_DIR%"
    echo.
    echo 建议解决方案:
    echo 1. 重启计算机后再次运行此脚本
    echo 2. 在安全模式下运行此脚本
    echo 3. 使用专门的文件解锁工具
    echo.
    echo 是否创建重启后自动删除脚本? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        call :create_reboot_script
    )
) else (
    echo.
    echo ✓ 所有文件删除成功！
)

echo.
pause
exit /b 0

:create_reboot_script
echo 正在创建重启后自动删除脚本...

:: 创建重启后运行的脚本
echo @echo off > "%USERPROFILE%\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\delete_esafenet.bat"
echo echo 正在重启后清理EsafeNet文件... >> "%USERPROFILE%\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\delete_esafenet.bat"
echo cd /d "%~dp0" >> "%USERPROFILE%\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\delete_esafenet.bat"
echo call "%~f0" >> "%USERPROFILE%\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\delete_esafenet.bat"
echo del "%%0" >> "%USERPROFILE%\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\delete_esafenet.bat"

echo ✓ 重启脚本已创建！
echo 重启计算机后将自动执行删除操作
goto :eof
