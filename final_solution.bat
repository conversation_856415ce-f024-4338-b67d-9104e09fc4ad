@echo off
echo ========================================
echo EsafeNet ProcessCtrl64.sys Final Solution
echo ========================================
echo.

:: Check admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Administrator privileges required
    echo Please right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo SUCCESS: Administrator privileges detected
echo.

set "TARGET_FILE=C:\Program Files\EsafeNet\Cobra DocGuard Client\ProcessCtrl64.sys"

echo Target file: %TARGET_FILE%
echo.

if not exist "%TARGET_FILE%" (
    echo SUCCESS: ProcessCtrl64.sys file does not exist, may have been deleted
    pause
    exit /b 0
)

echo Solution steps:
echo 1. Stop ProcessCtrl driver
echo 2. Disable driver auto-start
echo 3. Clean registry startup items
echo 4. Delete file
echo 5. If failed, create reboot delete script
echo.

echo Press any key to start execution...
pause
echo.

echo ========================================
echo Step 1: Stop ProcessCtrl driver
echo ========================================

echo Stopping ProcessCtrl driver...
sc stop ProcessCtrl >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: ProcessCtrl driver stopped
) else (
    echo WARNING: ProcessCtrl driver stop failed or does not exist
)

echo Stopping clasLock driver...
sc stop clasLock >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: clasLock driver stopped
) else (
    echo WARNING: clasLock driver stop failed or does not exist
)

timeout /t 3 >nul

echo.
echo ========================================
echo Step 2: Disable driver auto-start
echo ========================================

echo Disabling ProcessCtrl driver auto-start...
sc config ProcessCtrl start= disabled >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: ProcessCtrl driver disabled
) else (
    echo WARNING: ProcessCtrl driver disable failed
)

echo Disabling clasLock driver auto-start...
sc config clasLock start= disabled >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: clasLock driver disabled
) else (
    echo WARNING: clasLock driver disable failed
)

echo.
echo ========================================
echo Step 3: Clean registry startup items
echo ========================================

echo Cleaning registry startup items...

reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "CDGRegedit" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "NsEPSTray" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "CDGUPGRADE" /f >nul 2>&1

reg delete "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run" /v "CDGRegedit" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run" /v "NsEPSTray" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run" /v "CDGUPGRADE" /f >nul 2>&1

echo SUCCESS: Registry startup items cleaned

echo.
echo ========================================
echo Step 4: Attempt to delete file
echo ========================================

echo Attempting to delete ProcessCtrl64.sys...

:: Method 1: Take ownership and delete
takeown /f "%TARGET_FILE%" /a >nul 2>&1
icacls "%TARGET_FILE%" /grant administrators:F >nul 2>&1
attrib -r -s -h "%TARGET_FILE%" >nul 2>&1
del /f /q "%TARGET_FILE%" >nul 2>&1

if not exist "%TARGET_FILE%" (
    echo SUCCESS: ProcessCtrl64.sys deleted successfully!
    goto :success
)

echo ERROR: Standard method failed, trying PowerShell method...

:: Method 2: Use PowerShell force delete
powershell -Command "Remove-Item -Path '%TARGET_FILE%' -Force -ErrorAction SilentlyContinue" >nul 2>&1

if not exist "%TARGET_FILE%" (
    echo SUCCESS: ProcessCtrl64.sys deleted successfully!
    goto :success
)

echo ERROR: PowerShell method also failed

echo.
echo ========================================
echo Step 5: Create reboot delete script
echo ========================================

echo File still in use, creating reboot delete script...

:: 创建重启后删除脚本
set "STARTUP_DIR=%USERPROFILE%\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup"
set "REBOOT_SCRIPT=%STARTUP_DIR%\delete_processctrl_final.bat"

echo @echo off > "%REBOOT_SCRIPT%"
echo echo Deleting ProcessCtrl64.sys after reboot... >> "%REBOOT_SCRIPT%"
echo timeout /t 15 /nobreak ^>nul >> "%REBOOT_SCRIPT%"
echo takeown /f "%TARGET_FILE%" /a ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo icacls "%TARGET_FILE%" /grant administrators:F ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo attrib -r -s -h "%TARGET_FILE%" ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo del /f /q "%TARGET_FILE%" ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo if not exist "%TARGET_FILE%" ^( >> "%REBOOT_SCRIPT%"
echo     echo SUCCESS: ProcessCtrl64.sys deleted successfully! >> "%REBOOT_SCRIPT%"
echo ^) else ^( >> "%REBOOT_SCRIPT%"
echo     echo ERROR: ProcessCtrl64.sys still cannot be deleted >> "%REBOOT_SCRIPT%"
echo ^) >> "%REBOOT_SCRIPT%"
echo timeout /t 5 ^>nul >> "%REBOOT_SCRIPT%"
echo del "%%~f0" ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"

echo SUCCESS: Reboot delete script created
echo Script location: %REBOOT_SCRIPT%
echo.
echo Please restart computer, script will run automatically and delete file

goto :end

:success
echo.
echo ========================================
echo 🎉 任务完成！
echo ========================================
echo ✅ ProcessCtrl64.sys 文件已成功删除
echo ✅ EsafeNet驱动已停止并禁用
echo ✅ 注册表启动项已清理

:end
echo.
echo 按任意键退出...
pause
