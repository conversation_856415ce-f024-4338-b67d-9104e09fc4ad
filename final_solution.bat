@echo off
chcp 65001 >nul
echo ========================================
echo EsafeNet ProcessCtrl64.sys 最终解决方案
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击此文件并选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 检测到管理员权限
echo.

set "TARGET_FILE=C:\Program Files\EsafeNet\Cobra DocGuard Client\ProcessCtrl64.sys"

echo 🎯 目标文件: %TARGET_FILE%
echo.

if not exist "%TARGET_FILE%" (
    echo ✅ ProcessCtrl64.sys 文件不存在，可能已被删除
    pause
    exit /b 0
)

echo 📋 当前解决方案步骤:
echo 1. 停止ProcessCtrl驱动
echo 2. 禁用驱动自动启动
echo 3. 清理注册表启动项
echo 4. 删除文件
echo 5. 如果失败，创建重启后删除脚本
echo.

echo 按任意键开始执行...
pause
echo.

echo ========================================
echo 第1步: 停止ProcessCtrl驱动
echo ========================================

echo 正在停止ProcessCtrl驱动...
sc stop ProcessCtrl >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ProcessCtrl驱动已停止
) else (
    echo ⚠️  ProcessCtrl驱动停止失败或不存在
)

echo 正在停止clasLock驱动...
sc stop clasLock >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ clasLock驱动已停止
) else (
    echo ⚠️  clasLock驱动停止失败或不存在
)

timeout /t 3 >nul

echo.
echo ========================================
echo 第2步: 禁用驱动自动启动
echo ========================================

echo 正在禁用ProcessCtrl驱动自动启动...
sc config ProcessCtrl start= disabled >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ProcessCtrl驱动已禁用
) else (
    echo ⚠️  ProcessCtrl驱动禁用失败
)

echo 正在禁用clasLock驱动自动启动...
sc config clasLock start= disabled >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ clasLock驱动已禁用
) else (
    echo ⚠️  clasLock驱动禁用失败
)

echo.
echo ========================================
echo 第3步: 清理注册表启动项
echo ========================================

echo 正在清理注册表启动项...

reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "CDGRegedit" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "NsEPSTray" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "CDGUPGRADE" /f >nul 2>&1

reg delete "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run" /v "CDGRegedit" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run" /v "NsEPSTray" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run" /v "CDGUPGRADE" /f >nul 2>&1

echo ✅ 注册表启动项清理完成

echo.
echo ========================================
echo 第4步: 尝试删除文件
echo ========================================

echo 正在尝试删除ProcessCtrl64.sys...

:: 方法1: 获取所有权并删除
takeown /f "%TARGET_FILE%" /a >nul 2>&1
icacls "%TARGET_FILE%" /grant administrators:F >nul 2>&1
attrib -r -s -h "%TARGET_FILE%" >nul 2>&1
del /f /q "%TARGET_FILE%" >nul 2>&1

if not exist "%TARGET_FILE%" (
    echo ✅ ProcessCtrl64.sys 删除成功！
    goto :success
)

echo ❌ 标准方法删除失败，尝试PowerShell方法...

:: 方法2: 使用PowerShell强制删除
powershell -Command "Remove-Item -Path '%TARGET_FILE%' -Force -ErrorAction SilentlyContinue" >nul 2>&1

if not exist "%TARGET_FILE%" (
    echo ✅ ProcessCtrl64.sys 删除成功！
    goto :success
)

echo ❌ PowerShell方法也失败

echo.
echo ========================================
echo 第5步: 创建重启后删除脚本
echo ========================================

echo 文件仍然被占用，正在创建重启后删除脚本...

:: 创建重启后删除脚本
set "STARTUP_DIR=%USERPROFILE%\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup"
set "REBOOT_SCRIPT=%STARTUP_DIR%\delete_processctrl_final.bat"

echo @echo off > "%REBOOT_SCRIPT%"
echo chcp 65001 ^>nul >> "%REBOOT_SCRIPT%"
echo echo 正在重启后删除ProcessCtrl64.sys... >> "%REBOOT_SCRIPT%"
echo timeout /t 15 /nobreak ^>nul >> "%REBOOT_SCRIPT%"
echo takeown /f "%TARGET_FILE%" /a ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo icacls "%TARGET_FILE%" /grant administrators:F ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo attrib -r -s -h "%TARGET_FILE%" ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo del /f /q "%TARGET_FILE%" ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"
echo if not exist "%TARGET_FILE%" ^( >> "%REBOOT_SCRIPT%"
echo     echo ✅ ProcessCtrl64.sys 删除成功！ >> "%REBOOT_SCRIPT%"
echo ^) else ^( >> "%REBOOT_SCRIPT%"
echo     echo ❌ ProcessCtrl64.sys 仍然无法删除 >> "%REBOOT_SCRIPT%"
echo ^) >> "%REBOOT_SCRIPT%"
echo timeout /t 5 ^>nul >> "%REBOOT_SCRIPT%"
echo del "%%~f0" ^>nul 2^>^&1 >> "%REBOOT_SCRIPT%"

echo ✅ 重启后删除脚本已创建
echo 📍 脚本位置: %REBOOT_SCRIPT%
echo.
echo 🔄 请重启计算机，脚本将自动运行并删除文件

goto :end

:success
echo.
echo ========================================
echo 🎉 任务完成！
echo ========================================
echo ✅ ProcessCtrl64.sys 文件已成功删除
echo ✅ EsafeNet驱动已停止并禁用
echo ✅ 注册表启动项已清理

:end
echo.
echo 按任意键退出...
pause
